.php-cache: &php-cache
    key:
        prefix: php
        files:
            - composer.lock
    paths:
        - vendor/
    policy: pull
.php-prod-cache: &php-prod-cache
    key:
        prefix: php-prod
        files:
            - composer.lock
    paths:
        - vendor/
    policy: pull
.node-cache: &node-cache
    key:
        prefix: node
        files:
            - package-lock.json
    paths:
        - node_modules/
    policy: pull
.node-admin-cache: &node-admin-cache
    key:
        prefix: node-admin
        files:
            - www/admin/new/package-lock.json
    paths:
        - www/admin/new/node_modules
    policy: pull

default:
    image: $CI_REGISTRY/sk/docker-php:8.1
    interruptible: true
    tags:
        - arm
        - small

stages:
    - install
    - test
    - build
    - deploy

variables:
    GIT_DEPTH: 0
    FF_USE_FASTZIP: 1
    CACHE_COMPRESSION_LEVEL: 'fastest'

workflow:
    rules:
        -   if: $CI_PIPELINE_SOURCE == 'merge_request_event'
        -   if: $CI_COMMIT_BRANCH == 'master'
        -   if: $CI_COMMIT_TAG =~ /^prod-/

###

.install.php:
    stage: install
    rules:
        -   changes:
                - composer.lock
        -   if: $CI_COMMIT_MESSAGE =~ /\[install\]/
    before_script:
        - php composer.phar config -g cache-dir "$(pwd)/.composer"
    tags:
        - arm
        - large


.install.node:
    stage: install
    script:
        - npm ci --cache .npm --prefer-offline --no-audit --fund=false
    tags:
        - x86
        - large


install.php.ci:
    extends: .install.php
    cache:
        <<: *php-cache
        policy: push
    script:
        - php composer.phar install --no-interaction --ansi

install.php.prod:
    extends: .install.php
    cache:
        <<: *php-prod-cache
        policy: push
    script:
        - php composer.phar install --no-interaction --ansi --no-dev --prefer-dist --optimize-autoloader

install.node.frontend:
    extends: .install.node
    image: node:16
    rules:
        -   changes:
                - package-lock.json
        -   if: $CI_COMMIT_MESSAGE =~ /\[install\]/
    cache:
        <<: *node-cache
        policy: push

install.node.admin:
    extends: .install.node
    image: node:12
    rules:
        -   changes:
                paths:
                    - www/admin/new/package-lock.json
                compare_to: master
        -   if: $CI_COMMIT_MESSAGE =~ /\[install\]/
    cache:
        <<: *node-admin-cache
        policy: push
    before_script:
        - cd www/admin/new

###

.test.php:
    stage: test
    cache:
        <<: *php-cache
    needs:
        -   job: install.php.ci
            optional: true

test.php.linter:
    extends: .test.php
    script: php composer.phar run-script lint

test.php.latte-linter:
    extends: .test.php
    script: php composer.phar run-script latte-lint

test.php.phpstan:
    extends: .test.php
    script: php composer.phar run-script phpstan
    tags:
        - arm
        - large

#test.php.tests:
#    extends: .test.php
#    services:
#        - name: mariadb:10
#          alias: mysql
#          variables:
#              MYSQL_ROOT_PASSWORD: root
#              MYSQL_DATABASE: superadmin2019
#        - name: redis:latest
#          alias: redis
#    before_script:
#        - cp tests/config.ci.neon app/config/config.local.neon
#        - php bin/console migrations:continue
#    script: php composer.phar run-script tests

###

.build:
    stage: build
    script:
        - npm run build
    tags:
        - x86
        - small

build.frontend:
    extends: .build
    image: node:16
    needs:
        -   job: install.node.frontend
            optional: true
    cache:
        <<: *node-cache
    artifacts:
        paths:
            - www/static

build.admin:
    extends: .build
    image: node:12
    needs:
        -   job: install.node.admin
            optional: true
    cache:
        <<: *node-admin-cache
    before_script:
        - cd www/admin/new
    artifacts:
        paths:
            - www/admin/new/dist

###
.deploy:
    stage: deploy
    image: $CI_REGISTRY/sk/docker-php-deployer:v7.3.3
    interruptible: false
    resource_group: deploy
    cache:
        <<: *php-prod-cache
    dependencies:
        - build.admin
        - build.frontend
    before_script:
        - mkdir -p ~/.ssh
        - chmod 700 ~/.ssh
        - cp "$SSH_KNOWN_HOSTS" ~/.ssh/known_hosts
        - chmod 644 ~/.ssh/known_hosts
        - cp "$DEPLOYMENT_KEY" ~/.ssh/id_ed25519
        - chmod 400 ~/.ssh/id_ed25519

#deploy.development:
#    extends: .deploy
#    only:
#        - dev
#    environment:
#        name: development
#        url: https://ton-test.www6.superkoderi.cz
#    script:
#        - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $CI_COMMIT_SHORT_SHA > app/config/webVersion.neon'
#        - dep -f .deploy.php deploy

deploy.staging:
    extends: .deploy
    rules:
        -   if: $CI_COMMIT_BRANCH == 'master'
    environment:
        name: staging
        url: https://meziviny.www6.superkoderi.cz
    script:
        - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $CI_COMMIT_SHORT_SHA > app/config/webVersion.neon'
        - dep -f .deploy.php deploy

#deploy.production:
#    extends: .deploy
#    rules:
#        -   if: $CI_COMMIT_TAG =~ /^prod-/
#    environment:
#        name: production
#        url: https://ton.www6.superkoderi.cz
#    script:
#        - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $CI_COMMIT_SHORT_SHA > app/config/webVersion.neon'
#        - dep -f .deploy.php deploy
