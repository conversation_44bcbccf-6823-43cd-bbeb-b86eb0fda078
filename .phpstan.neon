parameters:
	level: 7
	paths:
		- app
#		- tests
	fileExtensions:
		- php
		- phpt
	ignoreErrors:
		- '#(Used c|C)onstant ROOT_DIR not found.#'
		- '#(Used c|C)onstant WWW_DIR not found.#'
		- '#(Used c|C)onstant APP_DIR not found.#'
		- '#(Used c|C)onstant FE_TEMPLATE_DIR not found.#'
		- '#(Used c|C)onstant RS_TEMPLATE_DIR not found.#'
		- '#(Used c|C)onstant IMAGES_DIR not found.#'
		- '#(Used c|C)onstant FILES_DIR not found.#'
		- '#(Used c|C)onstant TEMP_DIR not found.#'
		- '#(Used c|C)onstant CACHE_DIR not found.#'
		- '#(Used c|C)onstant LOG_DIR not found.#'
		- '#(Used c|C)onstant IS_CLI not found.#'
		- '#Cannot call method setTranslator\(\) on Nette.#'
		- '#Cannot call method add\(\) on Nette.#'
		- '#Access to an undefined property Nette\\ComponentModel\\IContainer::\$translator#'
		- '#Parameter \#1 \$translator of method Ublaboo\\DataGrid\\DataGrid::setTranslator\(\)#'
		- '#Nette\\Application\\UI\\Component::redirect\(\) expects#'
		- '#Nette\\Application\\UI\\Component::link\(\) expects#'
		- '#Call to an undefined method Nette\\Application\\UI\\Template::setTranslator#'
		- '#Access to an undefined property App\\Model\\ParameterValue|(ArrayIterator<string, App\\Model\\ParameterValue>&iterable<App\\Model\\ParameterValue>)::\$value.#'

		# forms
		- '#Call to an undefined method Nette\\ComponentModel\\IComponent::add#'
		- '#Call to an undefined method Nette\\ComponentModel\\IComponent::set#'
		- '#Call to an undefined method Nette\\ComponentModel\\IComponent::getControlPrototype\(\)#'
		- '#Cannot call method render\(\) on Nette\\Application\\UI\\Template|stdClass#'
		- '#InfosErpConnector::buildProductsCache\(\)#'

		# baseline todo fixme pls

		- message: '#Access to an undefined property Nextras\\Orm\\Entity\\IEntity::\$(id|internalName)#'
		  path: app/AdminModule/components/ProductForm/ProductFormBuilder.php

		- message: '#^Access to an undefined property Nextras\\Orm\\Entity\\Entity::\$name#'
		  path: app/FrontModule/components/ContactForm/ContactForm.php

		- message: '#^Negated boolean expression is always false#'
		  path: app/FrontModule/components/OrderBasket/OrderBasket.php

		- message: '#.*#' # fuck this file, seriously, fuck it
		  path: app/Model/Orm/TraitsEntity/hasParametersTrait.php


	excludePaths:
		- app/Console/OldProductSync

	checkMissingIterableValueType: false
	scanDirectories:
		- app
	checkTooWideReturnTypesInProtectedAndPublicMethods: true
	checkUninitializedProperties: false
