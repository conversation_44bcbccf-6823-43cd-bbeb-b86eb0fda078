#!/bin/bash

DB_HOST="zahradak_db"
DB_USER="$MARIADB_USER"
DB_PASS="$MARIADB_PASSWORD"
DB_NAME="$MARIADB_DATABASE"

function wait_for_db() {
  until mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "SELECT 1" &> /dev/null; do
    echo "Waiting for database connection..."
    sleep 5
  done
}

wait_for_db

mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;"

if ! mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME; SHOW TABLES;" | grep .; then
  echo "Database is empty. Importing dump.sql..."
  mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < /docker/db/dump.sql
  mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "TRUNCATE TABLE elastic_search_index;"
else
  echo "Database is not empty. Skipping import."
fi

