FROM php:8.1-apache

# Install git and other necessary packages
RUN apt-get update && apt-get install -y \
    git \
    unzip \
    && rm -rf /var/lib/apt/lists/*

COPY --from=mlocati/php-extension-installer /usr/bin/install-php-extensions /usr/local/bin
RUN IPE_GD_WITHOUTAVIF=1 install-php-extensions gd intl curl mbstring mysqli xdebug zip redis exif
# ^ use gd without avif, it currently takes ages to build (see https://github.com/mlocati/docker-php-extension-installer/issues/514)

ENV APACHE_DOCUMENT_ROOT /var/www/html/www
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf
RUN echo "memory_limit = 1G" >> /usr/local/etc/php/conf.d/memlimit.ini

RUN echo "ServerName meziviny.superkoders.test" >> /etc/apache2/apache2.conf

RUN a2enmod rewrite
