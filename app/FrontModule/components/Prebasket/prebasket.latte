{varType App\Model\ProductVariant $productVariant}
<div n:if="$productVariant">
	<h1 class="u-vhide">
		{_'title_prebasket'}
	</h1>
	<div class="b-prebasket">
		<div class="b-prebasket__product">
			<div class="b-prebasket__img">
				{* {if $productVariant->firstImage}
					{php $img = $productVariant->firstImage->getSize('sm')}
					<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy"/>
				{else}
					<img src="{$basePath}/static/img/noimg.svg" alt="" width="200" height="200" loading="lazy"/>
				{/if} *}
				{if $productVariant->firstImage}
					{include '../../templates/part/core/image.latte',
						img: $productVariant->firstImage,
						alt: $productVariant->name,
						srcset: ['200w' => '1x', '400w' => '2x'],
						sizes: '200px'
					}
				{else}
					<img src="/static/img/noimg.svg" alt="" loading="lazy" width="500" height="500">
				{/if}
			</div>
			<div class="b-prebasket__content">
				<h2 class="b-prebasket__annot">{_'basket_added'}</h2>
				<h3 class="b-prebasket__name h4">{$productVariant->name}</h3>
				<p class="b-prebasket__price">
					{$amount}&times;
					<strong>

						{$productVariant->priceWithVat($mutation, $priceLevel, $state)|priceFormat}
					</strong>
					<span>{_'price_with_tax'|noescape}</span>
				</p>
			</div>
		</div>
		<div class="b-order-buttons">
			<p class="b-order-buttons__prev">
				<a href="{plink $pages->eshop}" class="js-modal-close">
					<span class="btn__text">
						{_'btn_continue_shopping'}
					</span>
				</a>
			</p>
			<p class="b-order-buttons__next">
				<a href="{plink $pages->basket}" class="btn btn--lg">
					<span class="btn__text">
						{_'btn_enter_basket'}
					</span>
				</a>
			</p>
		</div>
	</div>
</div>

