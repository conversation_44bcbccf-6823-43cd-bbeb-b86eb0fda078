{php $control['form']->action .= "#frm-newsletterForm-form"}

{snippet form}
	{form form class=>'f-newsletter block-loader', data-naja=>'',  novalidate=>"novalidate"}
		<div class="b-banner u-inverse">
			<div class="b-banner__content u-text-content">
				<h2>{_'txt_newsletter_form_heading'}</h2>
				<p class="u-text-lg">{_'txt_newsletter_form_content'}</p>

				{control messageForForm, $flashes, $form}
				{include '../inp.latte', form: $form, name: email, pClass: 'inp-fix'}
				<p>
					<button type="submit" class="btn"><span class="btn__text">{_btn_send_me}</span></button>
				</p>
				{include '../inp.latte', form: $form, name: approve, required: true, pClass: 'inp-fix u-text-sm',  labelLang: 'txt_newsletter_form_checkbox'}
				
				{*ANTISPAM*}
				{if isset($form['antispamNoJs'])}
					<p class="{if $form['antispamNoJs']->hasErrors()}has-error{else}u-js-hide{/if}" data-controller="Antispam">
						<label n:name="antispamNoJs">
							{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
						</label>
						<span class="inp-fix">
							<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
						</span>
					</p>
				{/if}
				{*/ANTISPAM*}
			</div>

			<div class="block-loader__loader"></div>
		</div>


		{if isset($flashes[0]) && $flashes[0]->type == "ok" && $flashes[0]->message == "newsletterAdded"}
			<script>
				dataLayer.push({
					'event' : 'action.optin.newsletter'
				});
			</script>
		{/if}
	{/form}
{/snippet}
