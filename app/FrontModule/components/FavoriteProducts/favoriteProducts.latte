{varType App\Model\UserFavoriteProduct[] $favoriteProducts}
{varType App\Model\UserFavoriteProduct $favoriteProduct}

{snippet orderHistory}
{default $showAll = false}

{if $favoriteProducts->count()}
	<section class="c-products" data-naja data-naja-loader="body">
		<p n:foreach="$flashes as $msg" class="message message--{$msg->type}">
			{$msg->message|noescape}
		</p>
		<div class="c-products__list grid" n:snippet="messages">
			{foreach $favoriteProducts as $product}
				<div class="c-products__item grid__cell size--6-12@md size--4-12@xl">
					{include $presenter->getTemplateFile('/part/box/product.latte'), product => $product->product, loading => $iterator->counter > 3 ? 'lazy' : 'eager', showRemoveFromFavorite => true}
				</div>
			{/foreach}
		</div>
	</section>
	{control pager}
{else}
	<div class="message">
		{_'message_no_favorite_products'}
	</div>
{/if}

{/snippet}

