<?php declare(strict_types = 1);

namespace App\Console\Elastic;

use App\Model\ElasticSearch\Product\Facade;
use App\Model\Orm;
use LogicException;
use SuperKoderi\MutationHolder;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class ProductPriceUpdateCommand extends Command
{
	public function __construct(
		private Orm $orm,
		private Facade $productElasticFacade,
		private MutationHolder $mutationHolder,
	)
	{
		parent::__construct();
	}


	protected function configure(): void
	{
		$this->setName('elastic:index:productPriceUpdate')
			->setDescription('Recalculate price in all mutations for products');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		foreach ($this->orm->mutation->findAll() as $mutation) {
			if (($esIndex = $this->orm->esIndex->getProductLastActive($mutation)) !== null) {

				$this->orm->setMutation($esIndex->mutation);
				$this->mutationHolder->setMutation($esIndex->mutation);

				$this->productElasticFacade->fillPriceUpdate($esIndex);
			} else {
				throw new LogicException(sprintf("Missing EsIndex for '%s' mutation", $mutation->langCode));
			}
		}

		$output->writeLn('DONE');
		return self::SUCCESS;
	}

}
