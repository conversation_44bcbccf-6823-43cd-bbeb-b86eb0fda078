<div class="u-hide">
	<div data-Templates-target="libraryImageItem">
		{include $templates.'/part/box/image-item.latte',
			props: [
				data: [
					controller: 'File',
					imagelibrary-target: 'img',
					action: 'click->ImageLibrary#selectImage Templates:uploadFile@window->File#upload',
					file-url-value: '{uploadUrl}',
					file-id-value: 'newItemMarker',
				],
				img: '{imageSrc}',
				name: '{imageName}'
			]
		}
	</div>
</div>


{embed './overlay.latte', props: [
	id: 'library',
	title: 'Připojit obrázky z knihovny',
	data: [
		controller: 'ImageLibrary',
		imagelibrary-url-value: '/superadmin/library2/default?id=1&open=true&thickbox=true',
		action: 'ImageList:updateSelectedImages@window->ImageLibrary#updateSelectedImages ImageList:openLibrary@window->ImageLibrary#open Tiny:openLibrary@window->ImageLibrary#open CustomFieldImage:openLibrary@window->ImageLibrary#open LibrarySearch:searchStart->ImageLibrary#startLoading LibrarySearch:searchEnd->ImageLibrary#stopLoading Tree:goToStart->ImageLibrary#startLoading Tree:goToEnd->ImageLibrary#goTo Tree:treeAdjustStart->ImageLibrary#startLoading Tree:treeAdjustEnd->ImageLibrary#stopLoading',
	],
	classes: ['b-overlay--library', 'block-loader', 'is-loading']
], templates=>$templates}
	{block content}
		<div data-imagelibrary-target="content"></div>
		<div class="block-loader__loader"></div>
	{/block}
{/embed}
