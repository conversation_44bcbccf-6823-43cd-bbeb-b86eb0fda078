{varType App\Model\Tree $object}

{block #tree}
	{snippet tree}
		<div class="col-tree">
			{if isset($tree)}
				<div class="menu-tree" data-click="{link default}" data-create="{link create}" data-move="{link move}">
					<ul>
						{include 'part/menu.latte', 'menu' => $tree}
					</ul>
				</div>
			{/if}
		</div>
	{/snippet}
{/block}


{block #content}
	<div class="col-content" data-controller="Templates" data-action="ImageLibrary:newItem@window->Templates#newItem">
		{snippet editForm}
			{if $object && $object->id}
				{form editForm class => 'ajax-formx'}
					<div class="right u-mb-15" n:if="isset($object) && $object->alias && $showPreviewLink">
						{if $object->getPathSentence()}<strong>{$object->getPathSentence()}</strong>{/if}

						{* pro hp nezobrazim alias *}
						<a href="{$testUrl}/{if $object->level}{$object->alias}{/if}?show=1" target="_blank">{_preview_link}</a>
					</div>

					<ul class="message message-error" n:if="$form->hasErrors()">
						<li n:foreach="$form->errors as $error">{$error}</li>
					</ul>

					<h1>{if $object->name}{$object->name}{else}{_"Create new item"}{/if}</h1>

					{input id}
					{input lastEdited}

					<div class="grid-row">
						<p class="{if $object->hasBadTemplate}grid-1-3{else}grid-1-2{/if} reset">
							{var $regeneratealias = 'true'}
							{if $object->edited}{var $regeneratealias = 'false'}{/if}

							{label name /}<br>
							<span class="inp-fix">
								{input name class => 'inp-text w-full aliasName', data-value => $form['name']->value, data-pair => '#frm-editForm-nameTitle, #frm-editForm-nameAnchor', data-regeneratealias => $regeneratealias}
							</span>
						</p>

						{if $object->hasBadTemplate}
							<p class="grid-1-3 reset">
								{label firstTemplate /}<br>
								<span class="inp-fix inp-fix-select">
									{input firstTemplate class => 'inp-text w-full'}
								</span>
							</p>
						{/if}

						<p class="{if $object->hasBadTemplate}grid-1-3{else}grid-1-2{/if} reset no-label">
							<span class="inp-item inp-center">
								{input public: value: 1}
								{label public: /}
							</span>
						</p>
					</div>

					<div n:if="$object->foreignPages->count()">
					 	<span n:foreach="$object->foreignPages as $foreignPage">
					 		<a n:href="this, id=>$foreignPage->id">
					 			{$foreignPage->getRootMutation()->langCode}: {$foreignPage->name}
							</a>
					 	</span>
					</div>
					<div n:if="$object->wiredPagesAll->count()">
					 	<span n:foreach="$object->wiredPagesAll as $wiredPage">
					 		<a n:href="this, id=>$wiredPage->id">
					 			{$wiredPage->getRootMutation()->langCode}: {$wiredPage->name}
							</a>
					 	</span>
					</div>


					{*{php $pageTabs = $config['tabs']['pages']}*}
					{php $pageTabs = $pageTabs['pages']}
					<div class="menu-tabs">
						<ul class="reset">
							<li><a href="#tab-content">{_tab_content}</a></li>
							<li n:if="in_array('images', $pageTabs)"><a href="#tab-images">{_tab_images}</a></li>
							<li n:if="in_array('videos', $pageTabs)"><a href="#tab-videos">{_tab_videos}</a></li>
							<li n:if="in_array('files', $pageTabs)"><a href="#tab-files">{_tab_files}</a></li>
{*							<li n:if="in_array('params', $pageTabs) && $object->getParametersInRS->count()"><a href="#tab-params">{_tab_params}</a></li>*}
							<li n:if="in_array('links', $pageTabs)"><a href="#tab-links">{_tab_links}</a></li>
							<li n:if="in_array('comments', $pageTabs)"><a href="#tab-comments">{_tab_comments}</a></li>
							<li n:if="in_array('faqs', $pageTabs)"><a href="#tab-faqs">{_tab_faqs}</a></li>
							<li n:if="in_array('reviews', $pageTabs)"><a href="#tab-reviews">{_tab_clientReviews}</a></li>
							<li n:if="in_array('attproducts', $pageTabs)"><a href="#tab-attproducts">{_tab_products}</a></li>
							<li n:if="in_array('attproductsReview', $pageTabs)"><a href="#tab-attproductsReview">{_tab_products_review}</a></li>
							<li n:if="in_array('pages', $pageTabs)"><a href="#tab-pages">{_tab_pages}</a></li>
							<li n:if="in_array('linkedCategories', $pageTabs)"><a href="#tab-linkedCategories">{_tab_linkedCategories}</a></li>
							<li n:if="!$onMainMutation"><a href="#tab-wiredPages">{_tab_wiredPages}</a></li>
							<li n:if="$object->getCfScheme()"><a href="#tab-customfields">{_tab_customfields}</a></li>
							<li n:if="$object->getCCModules()"><a href="#tab-modular">{_tab_modular}</a></li>

							<li n:if="in_array('seo', $pageTabs)"><a href="#tab-seo">{_tab_seo}</a></li>
							<li n:if="in_array('settings', $pageTabs)"><a href="#tab-settings">{_tab_settings}</a></li>

							{*{if $object->template == 'Product:default'}*}
								{*<li n:if="in_array('products', $pageTabs)"><a href="#tab-products">{_tab_sorting}</a></li>*}
							{*{/if}*}
						</ul>
					</div>
					<div id="tab-content" class="tab-fragment">
						<p>
							{label annotation /}<br>
							<span class="inp-fix">
								{input annotation class => 'inp-text w-full'}
							</span>
							<span class="r">Formát odkazu: "jméno":http://link.cz</span>
						</p>
						<p>
							{label content /}<br>

					{* TEST button - add image to wysiwyg*}
	{*
							<div class="grid-row">
								<div class="grid-1-3">
									<a class="btn btn-icon-before thickbox" href="/superadmin/library/?id=2&paramValue=content&ref=>'page'"
									   data-library="attached-images999999" data-skbox-title="Knihovna">
										<span>
											<span class="icon icon-attachment"></span>
											{_butt_attach_image}
										</span>
									</a>
								</div>

								<div class="grid-1-3">
									<ul class="reset crossroad-images-param-values" id="attached-images999999"
										data-place="imagesR" style="margin-left: 15px;">
									</ul>
								</div>
							</div>
	*}


							<span class="inp-fix">
								{input content class => 'inp-text w-full wysiwyg'}
							</span>
						</p>

						<a n:href="Library:default id=>1, paramValue=>-1" id="js-media-library-trigger" class="btn btn-icon-before thickbox" data-skbox-title="Knihovna" data-library="attached-images" >
							<span>
								<span class="icon icon-attachment"></span>
								{_attach_images}
							</span>
						</a>
					</div>




{*					<div id="tab-params" class="tab-fragment" n:if="in_array('params', $pageTabs) && $object->getParametersInRS->count()">*}
{*						{include '../part/box/paramEdit.latte', paramUidList=>$object->getParametersInRS->fetchPairs(null, 'uid')}*}
{*					</div>*}


					<div id="tab-images" class="tab-fragment" n:if="in_array('images', $pageTabs)">
						<p class="btns-attached">
							<a n:href="Library:default id=>1, ref=>'page'" class="btn btn-icon-before thickbox" data-skbox-title="Knihovna" data-library="attached-images" >
								<span>
									<span class="icon icon-attachment"></span>
									{_attach_images}
								</span>
							</a>
							<em class="grey">{_connected} {count($object->images)} {translate}{count($object->images)|plural: "picture", "pictures", "pictures_more"}{/translate}</em>
						</p>
						<div class="crossroad-images crossroad-grid">
							<ul class="sortable reset" id="attached-images" data-place="images">
								{if $object->images}
									{foreach $object->images->toCollection() as $image}
										{include 'part/form/images.latte', 'data' => $image}
									{/foreach}
								{/if}
							</ul>
						</div>
					</div>

					<div id="tab-files" class="tab-fragment" n:if="in_array('files', $pageTabs)">
						<div class="crossroad-attached">
							<div class="holder {if !$object->files}hide{/if}">
								<div class="hd">
									<div class="grid-row">
										<p class="grid-1">
											{_name}
										</p>
									</div>
								</div>
								<div class="bd">
									<ul class="sortable reset" id="files">
										{if $object->files}
											{foreach $object->files->toCollection() as $file}
												{include 'part/form/files.latte', 'data' => $file}
											{/foreach}
										{/if}
									</ul>
								</div>
							</div>
							<div class="ft">
								<p>
									<span href="#" class="btn btn-icon-before">
										<span><span class="icon icon-plus"></span> {_add_button}</span>

										<input id="file_upload{$object->id}" class="file_upload" name="file_upload" type="file"
												   multiple="true" data-method="prepend"
												   data-script="{plink uploadFiles!}"
												   data-place="files" data-idref="{$object->id}"
												   data-pattern='<li id="${l}fileID{r}" class="uploadifive-queue-item"><div class="thumb js-handle"><span class="img"></span><div class="uploadify-progress"><div class="uploadify-progress-bar"></div></div></div></li>' />

									</span>
								</p>

							</div>
						</div>
					</div>

					<div id="tab-links" class="tab-fragment" n:if="in_array('links', $pageTabs)">
						<div class="crossroad-attached">
							<div class="holder {if !$object->links}hide{/if}">
								<div class="hd">
									<div class="grid-row">
										<p class="grid-2-5">
											{_link_url}
										</p>
										<p class="grid-2-5">
											{_link_text}
										</p>
										<p class="grid-1-5 center">
											{_link_new_window}
										</p>
									</div>
								</div>
								<div class="bd">
									<ul class="sortable reset" data-copy="links" data-pattern='{include "part/form/links.latte"}'>
										{if $object->links}
											{foreach $object->links as $k=>$link}
												{include 'part/form/links.latte', 'k' => $k, 'data' => $link}
											{/foreach}
										{/if}
									</ul>
								</div>
							</div>
							<div class="ft">
								<p>
									<a href="#" class="btn btn-icon-before" data-copy="links">
										<span><span class="icon icon-plus"></span> {_add_button}</span>
									</a>
								</p>
							</div>
						</div>
					</div>

					<div id="tab-videos" class="tab-fragment" n:if="in_array('videos', $pageTabs)">
						<div class="message message-info">
							<p>
								{_msg_video} <strong>YouTube</strong>
							</p>
						</div>
						<div class="crossroad-attached">
							<div class="holder {if !$object->videos}hide{/if}">
								<div class="hd">
									<div class="grid-row">
										<p class="grid-2-3">
											{_video_url}
										</p>
										<p class="grid-1-3">
											název
										</p>
									</div>
								</div>
								<div class="bd">
									<ul class="sortable reset" data-copy="videos" data-pattern='{include "part/form/videos.latte"}'>
										{if $object->videos}
											{foreach $object->videos as $k=>$video}
												{include 'part/form/videos.latte', 'k' => $k, 'data' => $video}
											{/foreach}
										{/if}
									</ul>
								</div>
							</div>
							<div class="ft">
								<p>
									<a href="#" class="btn btn-icon-before" data-copy="videos">
										<span><span class="icon icon-plus"></span> {_add_button}</span>
									</a>
								</p>
							</div>
						</div>

					</div>


					<div id="tab-comments" class="tab-fragment" n:if="in_array('comments', $pageTabs)">
						<div class="crossroad-attached">
							<div class="holder {if !isset($object) || !$object->comments}hide{/if}">
								<div class="hd">
									<div class="grid-row">
										<p class="grid-1-5">
											Datum
										</p>
										<p class="grid-1-5">
											Autor
										</p>
										<p class="grid-3-5 center">
											Komentář
										</p>
									</div>
								</div>
								<div class="bd">
									<ul class="reset" data-copy="comments" data-pattern='{include "part/form/comments.latte"}'>
										{if isset($object) && $object->comments}
											{foreach $object->comments as $k=>$comment}
												{include 'part/form/comments.latte', 'k' => $k, 'data' => $comment->toArray()}
											{/foreach}
										{/if}
									</ul>
								</div>
							</div>
							<div class="ft">
								<p>
									<a href="#" class="btn btn-icon-before" data-copy="comments">
										<span><span class="icon icon-plus"></span> {_add_button}</span>
									</a>
								</p>
							</div>
						</div>
					</div>


					<div id="tab-faqs" class="tab-fragment" n:if="in_array('faqs', $pageTabs)">
						<div class="crossroad-attached">
							<div class="holder {if !isset($object) || !$object->faqs}hide{/if}">
								<div class="hd">
									<div class="grid-row">
										<p class="grid-2-5">
											Dotaz
										</p>
										<p class="grid-3-5 center">
											Odpověď
										</p>
									</div>
								</div>
								<div class="bd">
									<ul class="sortable reset" data-copy="faqs" data-pattern='{include "part/form/faqs.latte"}'>
										{if isset($object) && $object->comments}
											{foreach $object->faqs->toCollection() as $k=>$faqs}
												{include 'part/form/faqs.latte', 'k' => $k, 'data' => $faqs->toArray()}
											{/foreach}
										{/if}
									</ul>
								</div>
							</div>
							<div class="ft">
								<p>
									<a href="#" class="btn btn-icon-before" data-copy="faqs">
										<span><span class="icon icon-plus"></span> {_add_button}</span>
									</a>
									<span class="r">Formát odkazu: "jméno":http://link.cz</span>

								</p>

							</div>

						</div>
					</div>



					<div id="tab-reviews" class="tab-fragment" n:if="in_array('reviews', $pageTabs)">
						<div class="crossroad-attached">
							<div class="holder {if !isset($object) || !$object->reviews}hide{/if}">
								<div class="hd">
									<div class="grid-row">
										<p class="grid-1-5">
											Jméno
										</p>
										<p class="grid-1-5">
											Pozice
										</p>
										<p class="grid-1-5">
											Firma
										</p>
										<p class="grid-2-5 center">
											Text
										</p>
									</div>
								</div>
								<div class="bd">
									<ul class="sortable reset" data-copy="reviews" data-pattern='{include "part/form/reviews.latte"}'>
										{if isset($object) && $object->reviews}
											{foreach $object->reviews->toCollection() as $k=>$reviews}
												{php  $image = NULL}
												{if $reviews->image}
													{php $image = $reviews->image}
												{/if}
												{include 'part/form/reviews.latte', 'k' => $k, 'data' => $reviews->toArray(), 'image'=>$image}
											{/foreach}
										{/if}
									</ul>
								</div>
							</div>
							<div class="ft">
								<p>
									<a href="#" class="btn btn-icon-before" data-copy="reviews">
										<span><span class="icon icon-plus"></span> {_add_button}</span>
									</a>
								</p>
							</div>
						</div>
					</div>


					<div id="tab-customfields" class="tab-fragment" n:if="$object->getCfScheme()">
						<div class="crossroad-attached">
							{*<div class="holder {if !isset($object) || !$object->id}hide{/if}">*}

							{include 'part/customFields.latte'}

						</div>
					</div>

					<div id="tab-modular" class="tab-fragment" n:if="$object->getCCModules()">
						<div class="crossroad-attached">
							{*<div class="holder {if !isset($object) || !$object->id}hide{/if}">*}

							{include 'part/customContent.latte'}

						</div>
					</div>

					<div id="tab-attproducts" class="tab-fragment" n:if="in_array('attproducts', $pageTabs)">

						<div class="crossroad-attached">
							<div class="holder {if !isset($object) || !$object->productsAll}hide{/if}">
								<div class="hd">
									Produkty zmíněné v článku
								</div>
								<div class="bd">
									<ul class="sortable reset" data-copy="products" data-pattern='{include "part/form/product.latte", key=>'products'}'>
										{foreach $object->productsAll as $k => $prodItem}
											{include 'part/form/product.latte', 'k' => $k, 'productName' => $prodItem->id,
												'productInfo' => $prodItem, key=>'products'}
										{/foreach}
									</ul>
								</div>
							</div>
							<div class="ft">
								<p>
									<a href="#" class="btn btn-icon-before" data-copy="products">
										<span><span class="icon icon-plus"></span> Připojit produkt</span>
									</a>
								</p>
							</div>
						</div>

					</div>



					<div id="tab-pages" class="tab-fragment" n:if="in_array('pages', $pageTabs)">
						{include "part/tabs/pages.latte" _form => $form,
							title=>'Připojené stránky',
							name=>'pages',
							buttonText=>'Připojit stránku',
							attachedItems=>$object->pages}
					</div>


					<div id="tab-linkedCategories" class="tab-fragment" n:if="in_array('linkedCategories', $pageTabs)">
						{include "part/tabs/pages.latte" _form => $form,
							title=>'Odkazované kategorie',
							name=>'linkedCategories',
							buttonText=>'Přidat kategorii',
							attachedItems=>$object->linkedCategories,
							showAdd=>!$object->linkedCategories->count()
							}
					</div>

					<div id="tab-wiredPages" class="tab-fragment" n:if="!$onMainMutation">
						{include "part/tabs/pages.latte" _form => $form,
							title=>'Rodičovské kategorie',
							name=>'wiredPages',
							buttonText=>"Přidat kategorii z {$defaultMutation->langCode} mutace",
							attachedItems=>$object->wiredPages,
							showAdd=>!$object->wiredPages->count()
							}
					</div>


					<div id="tab-seo" class="tab-fragment" n:if="in_array('seo', $pageTabs)">
						{include "part/tabs/seo.latte" _form => $form}
					</div>

					<div id="tab-settings" class="tab-fragment" n:if="in_array('settings', $pageTabs)">
						{include "part/tabs/settings.latte" _form => $form}
					</div>



				<div class="fixed-bar">
					<button class="btn btn-green btn-icon-before">
						<span><span class="icon icon-checkmark"></span> {_save_button}</span>
					</button>
					<a n:if="!$object->uid || $user->isInRole('developer')" n:href="delete $object->id" class="btn btn-red btn-icon-before btn-delete ajax">
						<span><span class="icon icon-close"></span> {_delete_button}</span>
					</a>
				</div>

				{/form}
			{/if}
		{/snippet}
		{include $templates . '/part/core/libraryOverlay.latte'}
	</div>
{/block}
{*
DEBUG UPLOADIFY
{form uploadForm}
	{input file_upload}
	{input save}
{/form}
*}
