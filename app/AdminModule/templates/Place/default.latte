{block #content}

<div class="box-title">
	<p class="r">
		<a n:href="Place:add" class="btn btn-icon-before">
			<span><span class="icon icon-plus"></span> {_new_place_button}</span>
		</a>
	</p>
	<h1>{_"Places"}</h1>
</div>
<br />

{form filterForm class => "form-filter"}
	<div class="grid-row">
		<p class="grid-2-5">
			{label name /}<br />
			<span class="inp-fix inp-fix">
				{input name class => 'inp-text w-full'}
			</span>
		</p>

		<div class="grid-1-5">
			<div class="grid-row">
				{label city /}<br />
				<span class="inp-fix">
					{input city class => 'inp-text w-full'}
				</span>
			</div>
		</div>

		<p class="grid-1-5">
			<br />
			<button class="btn btn-green btn-icon-before">
				<span><span class="icon icon-checkmark"></span> {_filter_button}</span>
			</button>
			<a n:href="clearFilter!" class="btn btn-red btn-icon-before" n:if="$filterSession->data">
				<span><span class="icon icon-close"></span> {_filter_cancel_button}</span>
			</a>
		</p>
	</div>
{/form}

<table>
	<thead>
		<tr>
			<th class="status"></th>
			<th>{_"place_name"}</th>
			<th>{_"place_city"}</th>
			<th>{_"place_street"}</th>
			<th>{_"place_type"}</th>
			<th>{_"place_gps"}</th>
			<th></th>
		</tr>
	</thead>
	<tbody>
	{foreach $places as $p}
		<tr class="clickable">
			<td class="status"></td>
			{*<td>{if $p->public}ano{else}<span class="red">ne</span>{/if}</td>*}
			<td>{$p->name}</td>
			<td>{$p->city}</td>
			<td>{$p->street}</td>
			<td>{$p->type}</td>
			<td>{if $p->lat && $p->lon}ano{else}<span class="red bold">chybí gps</span>{/if}</td>
			<td><a n:href="edit $p->id" class="icon icon-pencil"><span class="vhide">{_"edit"}</span></a></td>
		</tr>
	{/foreach}
	</tbody>
</table>

<div class="paging">
	<p class="l">
		{control pager:admin, showPages => "true"}
	</p>
	<p class="r">
		{control pager:admin, class => "r"}
	</p>
</div>
