includes:
	- environment.dev.neon

parameters:
	stageName: local

	admin:
		allowedIpRanges:
			- "**********/16" # default docker bridge range
			- "***********/16" # default orbstack range

	config:
		domainUrl: https://meziviny.superkoders.test/
		mutations:
			cs:
				domain: meziviny.superkoders.test
				urlPrefix: false
			en:
				domain: meziviny.superkoders.test
				urlPrefix: en
		elastica:
			config:
				host: meziviny_es
				port: 9200

	database:
		host: meziviny_db
		database: meziviny
		user: root
		password: root

	migrations:
		withDummyData: false

	redis:
		host: meziviny_redis


http:
	proxy:
		- *********/8

mail:
	host: meziviny_mailcatcher
	port: 1025
