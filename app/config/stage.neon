parameters:
	stageName: "stage"

	redis:
		dbIndex: 0
		password: ''

	config:
		isDev: false
		env: stage

		mutations:
			cs:
				domain: meziviny.www6.superkoderi.cz
				urlPrefix: false
				mutationId: 1
				rootId: 1
				eshopRootId: 21
				hidePageId: 43
				systemPageId: 398
				internalName: česky
				googleAnalyticsCode: 'GTM-5QDF4W2'
				robots: "index, follow"
				sitemap: true

		domainUrl: "https://meziviny.www6.superkoderi.cz/"  # https://halla.www4.superkoderi.cz TODO zmenit jak se spusti ostra domena
		domainUrlPdf: "https://meziviny.www6.superkoderi.cz/"  # https://halla.www4.superkoderi.cz TODO zmenit jak se spusti ostra domena

		translations:
			insertNew: true
			markUsage: true

includes:
    - header.php

extensions:
	sentry: Contributte\Logging\DI\SentryLoggingExtension
sentry:
	url: https://<EMAIL>/5973479
	options:
		environment: stage

services:
	cacheStorage:
		class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

	nette.latteFactory:
		setup:
#			- setTempDirectory("../temp/cache/latte")

	mysql.panel: Dibi\Bridges\Tracy\Panel

	tracy.bar:
		setup:
			- @mysql.panel::register(@database)

