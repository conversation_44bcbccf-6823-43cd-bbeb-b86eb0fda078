/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

DROP TABLE IF EXISTS `elastic_search_index`;
CREATE TABLE IF NOT EXISTS `elastic_search_index` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`mutationId` int(11) DEFAULT NULL,
	`type` varchar(255) COLLATE utf8mb4_czech_ci NOT NULL,
	`createdTime` datetime DEFAULT NULL,
	`startTime` datetime DEFAULT NULL,
	`finishTime` datetime DEFAULT NULL,
	`recreate` tinyint(1) DEFAULT '0',
	`status` text COLLATE utf8mb4_czech_ci,
	`active` int(11) DEFAULT '0',
	`errorCount` int(11) NOT NULL DEFAULT '0',
	`errorDetail` longtext COLLATE utf8mb4_czech_ci NOT NULL,
	PRIMARY KEY (`id`),
	KEY `mutationId` (`mutationId`),
	CONSTRAINT `elastic_search_index_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE SET NULL
	) ENGINE=InnoDB AUTO_INCREMENT=744 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
