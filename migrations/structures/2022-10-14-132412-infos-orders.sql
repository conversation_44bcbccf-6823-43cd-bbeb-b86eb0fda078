ALTER TABLE `order`
	ADD COLUMN `infosStatus` VARCHAR(255) NOT NULL DEFAULT '' AFTER `paymentStatus`;

ALTER TABLE `user`
	ADD COLUMN `infosCode` VARCHAR(250) NOT NULL AFTER `priceLevelId`;




ALTER TABLE `order_item`
	ADD COLUMN `infosCode` VARCHAR(255) NOT NULL DEFAULT '' AFTER `parentId`;
ALTER TABLE `order_item`
	ADD COLUMN `infosSarze` VARCHAR(255) NOT NULL DEFAULT '' AFTER `parentId`;

ALTER TABLE `order_item`
	ADD COLUMN `inAction` INT(11) NOT NULL DEFAULT '0' AFTER `originalUnitPrice`;
