ALTER TABLE `product`
	ADD COLUMN `isVoucher` TINYINT(4) NOT NULL AFTER `isAction`;


ALTER TABLE `product`
DROP FOREIGN KEY `product_ibfk_1`,
	DROP FOREIGN KEY `product_ibfk_2`;



ALTER TABLE `product_localization`
	ADD COLUMN `voucherId` INT(11) NULL AFTER `mutationId`;

ALTER TABLE `product_localization`
	ADD CONSTRAINT `FK_product_localization_voucher` FOREIGN KEY (`voucherId`) REFERENCES `voucher` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;
