ALTER TABLE `seolink_localization`
	ADD COLUMN `treeId` INT(11) NULL AFTER `seoLinkId`,
	ADD CONSTRAINT `seolink_localization_ibfk_3` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `seolink_localization`
DROP FOREIGN KEY `seolink_localization_ibfk_3`;
ALTER TABLE `seolink_localization`
	CHANGE COLUMN `treeId` `categoryId` INT(11) NULL DEFAULT NULL AFTER `seoLinkId`,
DROP INDEX `seolink_localization_ibfk_3`,
	ADD INDEX `seolink_localization_ibfk_3` (`categoryId`) USING BTREE,
	ADD CONSTRAINT `seolink_localization_ibfk_3` FOREIGN KEY (`categoryId`) REFERENCES `tree` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;



UPDATE `seolink_localization` SET `categoryId`='519';
UPDATE `seolink_localization` SET `categoryId`='21' WHERE  `id`=7;
UPDATE `seolink_localization` SET `categoryId`='21' WHERE  `id`=6;


UPDATE `tree` SET `uid`='eshopVines' WHERE  `id`=519;
