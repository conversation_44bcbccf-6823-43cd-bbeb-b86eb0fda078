DROP TABLE `db_cache`;
DROP TABLE `entity_log`;
DROP TABLE `example_category`;
DROP TABLE `example_item`;

DELETE FROM `discount_price` WHERE  `id`=1;
DELETE FROM `discount_price` WHERE  `id`=2;



DROP TABLE IF EXISTS `price_level`;
CREATE TABLE IF NOT EXISTS `price_level` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`type` varchar(50) NOT NULL,
	`name` varchar(100) NOT NULL,
	PRIMARY KEY (`id`),
	UNIQUE KEY `type` (`type`)
	) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;

REPLACE INTO `price_level` (`id`, `type`, `name`) VALUES
	(1, 'default', 'Základní'),
	(2, 'price10', 'Sleva 10%'),
	(3, 'price11', 'Sleva 11%'),
	(4, 'price12', '<PERSON>leva 12%'),
	(5, 'price13', 'Sleva 13%'),
	(6, 'price14', 'Sleva 14%'),
	(7, 'price15', 'Sleva 15%');



UPDATE `user` SET `priceLevelId`='2';


DELETE FROM `meziviny`.`discount` WHERE  `id`=1;
