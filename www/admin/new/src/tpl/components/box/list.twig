{% set props = {
	items: props.items | default([]),
	title: props.title | default,
	dragdrop: props.dragdrop | default(false),
	file: props.file | default(false),
	add: props.add | default(false),
	addData: props.addData | default(null),
	classes: props.classes | default(['u-mb-sm']),
	data: props.data | default(null),
	listData: props.listData | default(null),
} %}

{% set classes = [
	'b-list'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div
	class="{{ classes }}"
	{% if props.data is not null %}
		{% for key, value in props.data %}
			data-{{ key }}="{{ value }}"
		{% endfor %}
	{% endif %}
>
	{% if props.title %}
		<h2 class="b-std__title title">
			{{ props.title }}
		</h2>
	{% endif %}
	<div
		class="b-list__list"
		{% if props.listData is not null %}
			{% for key, value in props.listData %}
				data-{{ key }}="{{ value }}"
			{% endfor %}
		{% endif %}
	>
		{% for item in props.items %}
			{% include '@components/parts/list-item.twig' with {
				props: {
					texts: item.texts | default([]),
					inps: item.inps | default([]),
					img: item.img | default,
					checkboxes: item.checkboxes | default([]),
					btnsBefore: item.btnsBefore | default([]),
					btnsAfter: item.btnsAfter | default([]),
					tags: item.tags | default([]),
					langs: item.langs | default([]),
					data: item.data | default(null),
					dragdrop: props.dragdrop,
				}
			} %}
		{% endfor %}
	</div>
	{% if props.add %}
		<button
			type="button"
			class="b-list__add btn{% if props.file %} b-list__add--file{% endif %}"
			{% if props.addData is not null %}
				{% for key, value in props.addData %}
					data-{{ key }}="{{ value }}"
				{% endfor %}
			{% endif %}
		>
			{% include '@icons/plus.svg' %}
			{% if props.file %}<input type="file" multiple data-action="change->List#addFile">{% endif %}
		</button>
	{% endif %}
</div>

