export const radio = (parent, field, value, scheme) => {
	const { id: key } = field;
	const tplElement = document.createElement('div');
	const keyChunks = key.split('-');
	value = value ? value : scheme.defaultValue ? scheme.defaultValue : '';
	keyChunks.pop();
	parent.append(tplElement);
	tplElement.outerHTML = `
<div class="inp u-mb-sm" data-controller="CustomField" data-customfield-key-value='${key}'>
	${scheme.label ? `<span class="inp-label title">${scheme.label}</span>` : ''}
	<div class="inp-items${scheme.inline ? ' inp-items--inline' : ''}">
		<div class="inp-items__list">
		${scheme.options
			.map(
				(option) =>
					`<label class="inp-items__item inp-item inp-item--radio u-mb-xs">
						<input
							type="radio"
							class="inp-item__inp"
							name="cutom-${key}"
							id="custom-${key}"
							value="${option.value}"
							${value === option.value ? ' checked' : ''}
							data-action="change->CustomField#updateValue${scheme.isContentToggle ? ' change->contenttoggle#toggle' : ''}"
							${scheme.isContentToggle ? ` data-contenttoggle-target="toggle" data-target-id="${keyChunks.join('-')}-${option.value}"` : ''}
						>
						<span class="inp-item__text">${option.label}</span>
					</label>`,
			)
			.join('')}
		</div>
	</div>
</div>
`;
};
