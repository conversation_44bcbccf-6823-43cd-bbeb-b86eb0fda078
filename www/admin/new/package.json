{"name": "start_project", "version": "4.4.0", "sideEffects": ["**/svg4everybody.js"], "browserslist": ["last 3 versions", "not < 0.01%", "not ie <= 10", "since 2019", "not BlackBerry > 7", "not QQAndroid > 1", "Android > 4.4.1"], "engines": {"node": ">=14.19.0", "npm": ">=6.14.0"}, "devDependencies": {"@babel/core": "7.9.0", "@babel/plugin-proposal-class-properties": "7.13.0", "@babel/plugin-transform-runtime": "7.8.3", "@babel/preset-env": "7.9.6", "@superkoders/eslint-config": "1.2.25", "@superkoders/prettier-config": "0.2.6", "@superkoders/stylelint-config": "1.2.3", "ansi-colors": "4.1.1", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "babel-loader-exclude-node-modules-except": "1.0.3", "browser-sync": "2.26.7", "cheerio": "1.0.0-rc.3", "dateformat": "^3.0.3", "deepmerge": "4.0.0", "del": "5.1.0", "eslint": "6.5.0", "fancy-log": "1.3.3", "gulp": "4.0.2", "gulp-cheerio": "^0.6.3", "gulp-consolidate": "^0.2.0", "gulp-html-beautify": "1.0.1", "gulp-iconfont": "10.0.3", "gulp-imagemin": "6.1.0", "gulp-notify": "^3.2.0", "gulp-plumber": "^1.2.1", "gulp-postcss": "^8.0.0", "gulp-rename": "^1.4.0", "gulp-sass": "^4.0.2", "gulp-sass-vars": "^1.3.0", "gulp-svgmin": "^2.2.0", "gulp-svgstore": "^7.0.1", "gulp-twing": "4.0.0", "gulp-w3cjs": "^1.3.2", "gulp-zip": "^5.0.0", "gulp.spritesmith": "^6.11.0", "husky": "4.2.3", "import-fresh": "3.1.0", "lint-staged": "10.0.8", "node-notifier": "6.0.0", "node-sass-glob-importer": "5.3.2", "select2": "4.1.0-rc.0", "through2": "^3.0.1", "tinymce": "5.8.0", "twing": "5.0.2", "vinyl": "2.2.0", "webpack": "4.41.0"}, "dependencies": {"@fortawesome/fontawesome-free": "5.15.4", "@superkoders/modal": "1.2.3", "@superkoders/sk-tools": "1.3.0", "bootstrap": "^4.4.1", "bootstrap-datepicker": "^1.9", "bootstrap-select": "^1.13", "happy-inputs": "2.1.0", "jquery": "^3.4.1", "jquery-ui-sortable": "^1.0", "naja": "2.3.0", "nanoid": "3.1.23", "nette-forms": "^3.0", "popper.js": "^1.14.7", "sortablejs": "1.13.0", "stimulus": "2.0.0", "stimulus-use": "0.25.4", "svg4everybody": "^2.1.9", "ublaboo-datagrid": "6.9.1"}, "scripts": {"preversion": "npm run build", "version": "git add -A", "postversion": "git push --follow-tags", "export": "npx gulp export", "build": "npx gulp min", "dev": "npx gulp", "prestart": "npm install", "preinstall": "npx check-engine", "start": "npm run dev", "lint:css": "stylelint --syntax scss \"src/**/*.{css,scss}\"; exit 0", "lint:css:fix": "prettier-stylelint --write \"src/**/*.{css,scss}\"", "lint:js": "eslint .; exit 0", "lint:js:fix": "eslint . --fix", "print-version": "echo $npm_package_version"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.scss": ["stylelint --syntax scss \"*.{css,scss}\""], "*.js": ["eslint"]}, "volta": {"node": "14.19.0", "npm": "6.14.0"}}