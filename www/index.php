<?php declare(strict_types = 1);

use Apitte\Core\Application\IApplication;
use App\Bootstrap;
use Nette\Application\Application;



if (isset($_GET['info'])) {
	echo phpinfo();
	die;
}

require __DIR__ . '/../vendor/autoload.php';

$isApi = substr($_SERVER['REQUEST_URI'], 0, 5) === '/api/';
$container = Bootstrap::boot()->createContainer();

if ($isApi) {
	$container->getByType(IApplication::class)->run();
} else {
	$container->getByType(Application::class)->run();
}

