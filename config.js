const basePath = {
	src: 'src/',
	dest: 'www/static/',
	assets: '../',
};

const src = {
	fonts: `${basePath.src}fonts/`,
	icons: `${basePath.src}img/icons/`,
	images: `${basePath.src}img/`,
	favicon: `${basePath.src}favicon/`,
	scripts: `${basePath.src}js/`,
	styles: `${basePath.src}css/`,
	templates: `${basePath.src}tpl/`,
	components: `${basePath.src}tpl/components/`,
	layout: `${basePath.src}tpl/layout/`,
};

const twigNamespaces = {
	components: src.components,
	layout: src.layout,
	images: src.images,
	templates: src.templates,
};

const dest = {
	fonts: `${basePath.dest}fonts/`,
	images: `${basePath.dest}img/`,
	scripts: `${basePath.dest}js/`,
	styles: `${basePath.dest}css/`,
	templates: `${basePath.dest}tpl/`,
};

const assets = {
	fonts: `${basePath.assets}fonts/`,
	images: `${basePath.assets}img/`,
	favicon: `${basePath.assets}favicon/`,
	scripts: `${basePath.assets}js/`,
	dynamicScripts: `/static/js/`,
	styles: `${basePath.assets}css/`,
};

const webpack = {
	stats: {
		colors: true,
		hash: false,
		timings: true,
		assets: true,
		chunks: false,
		chunkModules: false,
		modules: false,
		children: true,
		version: false,
	},
};

const browserSync = {
	open: false,
	notify: false,
	reloadThrottle: 1000,
	watch: true,
	server: {
		baseDir: basePath.dest,
	},
};

const favicon = {
	src: 'favicon.svg',
	faviconDestPath: 'www/',
	tplPathCode: '{$mutation->getBaseUrl()}/static/favicon',
	manifest: {
		name: 'MEZ/VÍNY',
		short_name: 'MEZ/VÍNY',
		start_url: '/',
		theme_color: '#c59d5f',
		background_color: '#ffffff',
		description: 'Stylový wine shop & bistro v centru Poruby',
		orientation: 'portrait',
	},
};

module.exports = {
	basePath,
	src,
	dest,
	assets,
	twigNamespaces,
	webpack,
	browserSync,
	favicon,
	mediaQueries: {
		breakpoints: {
			sm: '480px',
			md: '750px',
			lg: '1000px',
			xl: '1290px',
		},
		rules: {
			webkit: '(-webkit-min-device-pixel-ratio: 0)',
			retina: '(-webkit-min-device-pixel-ratio: 2), (min--moz-device-pixel-ratio: 2), (-o-min-device-pixel-ratio: 2/1), (min-device-pixel-ratio: 2), (min-resolution: 192dpi), (min-resolution: 2dppx)',
		},
	},
};
