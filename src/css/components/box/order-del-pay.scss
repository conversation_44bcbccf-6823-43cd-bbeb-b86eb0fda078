@use 'base/variables';

.b-order-del-pay {
	&__list {
		margin: 0;
		padding: 0;
		border: 1px solid variables.$color-sg-gray;
		list-style: none;

		&:last-child {
			border-bottom: none;
		}

		li {
			margin: 0;
			padding: 0;
			border-bottom: 1px solid variables.$color-sg-gray;
			background: none;
		}
	}

	&__label {
		position: relative;
		display: block;
		padding: 15px 15px 15px 45px;
		transition: background-color variables.$t ease;
		cursor: pointer;

		&:hover {
			background-color: variables.$color-bg-light;
		}

		&-grid {
			display: flex;

			& > span:not(:last-child) {
				margin-right: 10px;
			}
		}

		& > .inp-item__text::before,
		& > .inp-item__text::after {
			position: absolute;
			top: 18px;
			left: 15px;
		}
	}

	&__name {
		flex-grow: 1;
	}

	&__time {
		display: block;
		font-size: 14px;
	}

	&__price {
		font-weight: bold;
	}

	input[type='radio']:disabled ~ * {
		opacity: 0.5;
	}
}
