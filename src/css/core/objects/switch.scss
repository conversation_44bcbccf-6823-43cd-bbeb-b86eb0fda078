@use 'base/variables';

.switch {
	position: relative;
	display: flex;
	align-items: center;
	user-select: none;
	&__inp {
		position: absolute;
		left: -5000px;
	}
	&__label {
		margin-right: auto;
		padding-right: 20px;
		color: rgba(variables.$color-white, 0.6);
	}
	&__inner {
		position: relative;
		display: flex;
		border-radius: 30px;
		background: #b6b6b6;
		transition: background-color variables.$t;
		cursor: pointer;
	}
	&__text {
		display: flex;
		flex: 1 1 auto;
		justify-content: center;
		align-items: center;
		width: 21px;
		height: 23px;
		text-align: center;
		opacity: 0;
		transition: opacity variables.$t;
		.icon-svg {
			position: relative;
			z-index: 1;
			svg {
				fill: currentcolor;
			}
		}
	}
	&__tool {
		position: absolute;
		top: 2px;
		bottom: 2px;
		left: 2px;
		width: 19px;
		border: 1px solid #e5e7eb;
		border-radius: 50%;
		background: #ffffff;
		transition: transform variables.$t;
	}

	// MODIF
	&__text--right {
		opacity: 1;
	}

	// STATES
	&__inp:focus-visible + &__inner {
		outline: variables.$focus-outline-width variables.$focus-outline-style variables.$focus-outline-color;
	}
	&__inp:checked + &__inner {
		background-color: variables.$color-sg-green-dark;
	}

	&__inp:checked + &__inner &__tool {
		transform: translateX(100%);
	}
	&__inp:checked + &__inner &__text--left {
		opacity: 1;
	}
	&__inp:checked + &__inner &__text--right {
		opacity: 0;
	}
	&__inp:disabled + &__inner {
		opacity: 0.5;
		pointer-events: none;
	}
}
